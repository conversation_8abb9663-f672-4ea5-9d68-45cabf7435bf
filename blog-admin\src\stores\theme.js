import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useLocaleStore } from './locale'

export const useThemeStore = defineStore('theme', () => {
  // 默认主题配置
  const defaultConfig = {
    theme: 'light',
    primaryColor: '#409EFF',
    headerBg: '#ffffff',
    headerTextColor: '#303133',
    sidebarBg: '#304156',
    sidebarTextColor: '#bfcbd9',
    showFooter: true,
    showBreadcrumb: true,
    showTabs: false,
    borderColor: '#e4e7ed'
  }

  // 主题配置
  const config = ref({ ...defaultConfig })

  // 预设主题
  const presetThemes = {
    light: {
      theme: 'light',
      primaryColor: '#409EFF',
      headerBg: '#ffffff',
      headerTextColor: '#303133',
      sidebarBg: '#ffffff',
      sidebarTextColor: '#303133',
      borderColor: '#e4e7ed'
    },
    dark: {
      theme: 'dark',
      primaryColor: '#409EFF',
      headerBg: '#1f2937',
      headerTextColor: '#f9fafb',
      sidebarBg: '#111827',
      sidebarTextColor: '#d1d5db',
      borderColor: '#374151'
    },
    blue: {
      theme: 'blue',
      primaryColor: '#1890ff',
      headerBg: '#001529',
      headerTextColor: '#ffffff',
      sidebarBg: '#001529',
      sidebarTextColor: '#ffffff',
      borderColor: '#303030'
    }
  }

  // 计算属性
  const isDark = computed(() => config.value.theme === 'dark')
  const isLight = computed(() => config.value.theme === 'light')
  const isBlue = computed(() => config.value.theme === 'blue')

  // 应用主题到CSS变量
  const applyTheme = () => {
    const root = document.documentElement
    const theme = config.value

    // 设置CSS变量
    root.style.setProperty('--el-color-primary', theme.primaryColor)
    root.style.setProperty('--header-bg', theme.headerBg)
    root.style.setProperty('--header-text-color', theme.headerTextColor)
    root.style.setProperty('--sidebar-bg', theme.sidebarBg)
    root.style.setProperty('--sidebar-text-color', theme.sidebarTextColor)
    root.style.setProperty('--border-color', theme.borderColor)

    // 设置body类名
    document.body.className = `theme-${theme.theme}`
  }

  // 设置预设主题
  const setPresetTheme = (themeName) => {
    if (presetThemes[themeName]) {
      config.value = {
        ...config.value,
        ...presetThemes[themeName]
      }
      applyTheme()
      saveToStorage()
    }
  }

  // 更新主题配置
  const updateConfig = (newConfig) => {
    config.value = { ...config.value, ...newConfig }
    applyTheme()
    saveToStorage()
  }

  // 重置主题
  const resetTheme = () => {
    config.value = { ...defaultConfig }
    applyTheme()
    saveToStorage()
    
    const localeStore = useLocaleStore()
    return localeStore.t('theme.themeReset')
  }

  // 复制配置
  const copyConfig = async () => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(config.value, null, 2))
      const localeStore = useLocaleStore()
      return localeStore.t('theme.configCopied')
    } catch (error) {
      console.error('Failed to copy config:', error)
      const localeStore = useLocaleStore()
      return localeStore.t('theme.copyFailed')
    }
  }

  // 保存到本地存储
  const saveToStorage = () => {
    localStorage.setItem('admin-theme', JSON.stringify(config.value))
  }

  // 从本地存储加载
  const loadFromStorage = () => {
    try {
      const stored = localStorage.getItem('admin-theme')
      if (stored) {
        const parsedConfig = JSON.parse(stored)
        config.value = { ...defaultConfig, ...parsedConfig }
      }
    } catch (error) {
      console.error('Failed to load theme from storage:', error)
      config.value = { ...defaultConfig }
    }
    
    applyTheme()
  }

  return {
    config: computed(() => config.value),
    presetThemes,
    isDark,
    isLight,
    isBlue,
    setPresetTheme,
    updateConfig,
    resetTheme,
    copyConfig,
    loadFromStorage
  }
})
