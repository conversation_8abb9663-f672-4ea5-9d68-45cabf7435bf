<template>
  <div class="admin-layout" :class="[`theme-${currentTheme}`, { 'is-fullscreen': isFullscreen }]">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside
        :width="sidebarWidth"
        class="sidebar"
        :class="{ 'is-collapse': isCollapse }"
      >
        <div class="logo">
          <img v-if="!isCollapse" src="/logo.svg" alt="Logo" class="logo-img" />
          <img v-else src="/logo.svg" alt="Logo" class="logo-img-mini" />
          <h2 v-if="!isCollapse" class="logo-text">博客管理系统</h2>
        </div>

        <el-scrollbar class="sidebar-scrollbar">
          <el-menu
            :default-active="$route.path"
            :collapse="isCollapse"
            :unique-opened="true"
            router
            :background-color="menuColors.background"
            :text-color="menuColors.text"
            :active-text-color="menuColors.activeText"
            class="sidebar-menu"
          >
            <el-menu-item index="/dashboard">
              <el-icon><DataBoard /></el-icon>
              <template #title>仪表盘</template>
            </el-menu-item>

            <el-sub-menu index="content">
              <template #title>
                <el-icon><Document /></el-icon>
                <span>内容管理</span>
              </template>
              <el-menu-item index="/articles">文章管理</el-menu-item>
              <el-menu-item index="/categories">分类管理</el-menu-item>
              <el-menu-item index="/tags">标签管理</el-menu-item>
            </el-sub-menu>

            <el-menu-item index="/users">
              <el-icon><User /></el-icon>
              <template #title>用户管理</template>
            </el-menu-item>

            <el-menu-item index="/comments">
              <el-icon><ChatDotRound /></el-icon>
              <template #title>评论管理</template>
            </el-menu-item>

            <el-sub-menu index="system">
              <template #title>
                <el-icon><Setting /></el-icon>
                <span>系统管理</span>
              </template>
              <el-menu-item index="/settings">系统设置</el-menu-item>
              <el-menu-item index="/logs">操作日志</el-menu-item>
            </el-sub-menu>
          </el-menu>
        </el-scrollbar>
      </el-aside>

      <el-container class="main-container">
        <!-- 顶部导航栏 -->
        <el-header class="header" :style="headerStyle">
          <div class="header-left">
            <!-- 折叠按钮 -->
            <el-button
              type="text"
              @click="toggleSidebar"
              class="collapse-btn"
              :class="{ 'is-active': isCollapse }"
            >
              <el-icon><Expand v-if="isCollapse" /><Fold v-else /></el-icon>
            </el-button>

            <!-- 面包屑导航 -->
            <el-breadcrumb separator="/" class="breadcrumb">
              <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item v-if="$route.meta.title">
                {{ $route.meta.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>

          <div class="header-right">
            <!-- 语言切换 -->
            <el-dropdown @command="handleLanguageChange" class="language-dropdown">
              <el-button type="text" class="header-btn">
                <span class="language-flag">{{ localeFlag }}</span>
                <span class="language-name">{{ localeName }}</span>
                <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="lang in availableLocales"
                    :key="lang.locale"
                    :command="lang.locale"
                    :class="{ 'is-active': lang.locale === currentLocale }"
                  >
                    <span class="language-flag">{{ lang.flag }}</span>
                    <span>{{ lang.name }}</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 主题切换 -->
            <el-tooltip :content="t('header.themeSettings')" placement="bottom">
              <el-button type="text" @click="showThemeDrawer = true" class="header-btn">
                <el-icon><Brush /></el-icon>
              </el-button>
            </el-tooltip>

            <!-- 全屏切换 -->
            <el-tooltip :content="isFullscreen ? t('header.exitFullscreen') : t('header.fullscreen')" placement="bottom">
              <el-button type="text" @click="toggleFullscreen" class="header-btn">
                <el-icon><FullScreen v-if="!isFullscreen" /><Aim v-else /></el-icon>
              </el-button>
            </el-tooltip>

            <!-- 刷新页面 -->
            <el-tooltip :content="t('header.refreshPage')" placement="bottom">
              <el-button type="text" @click="refreshPage" class="header-btn">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </el-tooltip>

            <!-- 用户信息 -->
            <el-dropdown @command="handleUserCommand" class="user-dropdown">
              <div class="user-info">
                <el-avatar :size="32" :src="userInfo.avatar" class="user-avatar">
                  {{ userInfo.name.charAt(0) }}
                </el-avatar>
                <span class="username">{{ userInfo.name }}</span>
                <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人中心
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon><Setting /></el-icon>
                    账户设置
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 主内容区 -->
        <el-main class="main-content">
          <div class="content-wrapper">
            <router-view v-slot="{ Component }">
              <transition name="fade-transform" mode="out-in">
                <component :is="Component" />
              </transition>
            </router-view>
          </div>
        </el-main>

        <!-- 底部 -->
        <el-footer class="footer" v-if="showFooter">
          <div class="footer-content">
            <span>© 2024 个人博客管理系统</span>
            <span>版本 v1.0.0</span>
          </div>
        </el-footer>
      </el-container>
    </el-container>

    <!-- 主题设置抽屉 -->
    <ThemeDrawer v-model="showThemeDrawer" />
  </div>
</template>

<script setup >
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useThemeStore } from '../stores/theme'
import { useUserStore } from '../stores/user'
import { useLocaleStore } from '../stores/locale'
import ThemeDrawer from '../layout/components/ThemeDrawer.vue'

const router = useRouter()
const themeStore = useThemeStore()
const userStore = useUserStore()
const localeStore = useLocaleStore()

// 响应式数据
const isCollapse = ref(false)
const isFullscreen = ref(false)
const showThemeDrawer = ref(false)

// 计算属性
const currentTheme = computed(() => themeStore.theme)
const showFooter = computed(() => themeStore.showFooter)
const userInfo = computed(() => userStore.userInfo)

// 语言相关
const currentLocale = computed(() => localeStore.locale)
const localeName = computed(() => localeStore.localeName)
const localeFlag = computed(() => localeStore.localeFlag)
const availableLocales = computed(() => localeStore.availableLocales)
const t = localeStore.t

const sidebarWidth = computed(() => {
  return isCollapse.value ? '64px' : '200px'
})

const menuColors = computed(() => {
  const themes = {
    light: {
      background: '#ffffff',
      text: '#303133',
      activeText: '#409EFF'
    },
    dark: {
      background: '#304156',
      text: '#bfcbd9',
      activeText: '#409EFF'
    },
    blue: {
      background: '#1890ff',
      text: '#ffffff',
      activeText: '#ffd700'
    }
  }
  return themes[currentTheme.value] || themes.light
})

const headerStyle = computed(() => {
  return {
    backgroundColor: themeStore.headerBg,
    color: themeStore.headerTextColor,
    borderBottom: `1px solid ${themeStore.borderColor}`
  }
})

// 方法
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

const refreshPage = () => {
  window.location.reload()
}

const handleLanguageChange = (locale) => {
  localeStore.setLocale(locale)
  ElMessage.success(t('common.success'))
}

const handleUserCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人中心功能开发中...')
      break
    case 'settings':
      ElMessage.info('账户设置功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await userStore.logout()
        ElMessage.success('退出成功')
        router.push('/login')
      } catch {
        // 用户取消
      }
      break
  }
}

const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  // 初始化主题和语言设置
  themeStore.loadFromStorage()
  localeStore.loadFromStorage()
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<style scoped>
/* 全局布局 */
.admin-layout {
  height: 100vh;
  overflow: hidden;
}

.admin-layout.is-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
}

/* 侧边栏样�?*/
.sidebar {
  background-color: var(--admin-sidebar-bg, #304156);
  transition: width 0.3s ease;
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1001;
}

.sidebar.is-collapse {
  width: 64px !important;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.2);
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  position: relative;
  overflow: hidden;
}

.logo-img {
  height: 32px;
  width: 32px;
  margin-right: 12px;
}

.logo-img-mini {
  height: 28px;
  width: 28px;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  white-space: nowrap;
  transition: opacity 0.3s;
}

.logo-text-mini {
  font-size: 20px;
  font-weight: 700;
}

.sidebar-scrollbar {
  height: calc(100vh - 60px);
}

.sidebar-menu {
  border-right: none;
  background-color: transparent;
}

/* 主容�?*/
.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 头部样式 */
.header {
  background-color: var(--admin-header-bg, #ffffff);
  color: var(--admin-header-text, #303133);
  border-bottom: 1px solid var(--admin-border-color, #e4e7ed);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.collapse-btn {
  font-size: 18px;
  color: inherit;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.collapse-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.collapse-btn.is-active {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.breadcrumb {
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-btn {
  font-size: 16px;
  color: inherit;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.header-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #409EFF;
}

/* 语言切换 */
.language-dropdown {
  margin-right: 8px;
}

.language-flag {
  font-size: 16px;
  margin-right: 6px;
}

.language-name {
  font-size: 14px;
  margin-right: 4px;
}

.user-dropdown {
  margin-left: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s;
}

.user-info:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.user-avatar {
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.username {
  font-size: 14px;
  font-weight: 500;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-icon {
  font-size: 12px;
  transition: transform 0.3s;
}

.user-dropdown.is-opened .dropdown-icon {
  transform: rotate(180deg);
}

/* 主内容区 */
.main-content {
  background-color: #f0f2f5;
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.content-wrapper {
  min-height: 100%;
}

/* 底部样式 */
.footer {
  background-color: var(--admin-header-bg, #ffffff);
  border-top: 1px solid var(--admin-border-color, #e4e7ed);
  padding: 12px 20px;
  text-align: center;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.08);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

/* 页面过渡动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 主题样式 */
.theme-light {
  --admin-header-bg: #ffffff;
  --admin-header-text: #303133;
  --admin-sidebar-bg: #ffffff;
  --admin-sidebar-text: #303133;
  --admin-border-color: #e4e7ed;
}

.theme-dark {
  --admin-header-bg: #1f2937;
  --admin-header-text: #f9fafb;
  --admin-sidebar-bg: #304156;
  --admin-sidebar-text: #bfcbd9;
  --admin-border-color: #374151;
}

.theme-blue {
  --admin-header-bg: #1890ff;
  --admin-header-text: #ffffff;
  --admin-sidebar-bg: #1890ff;
  --admin-sidebar-text: #ffffff;
  --admin-border-color: #40a9ff;
}

/* 响应式设�?*/
@media (max-width: 768px) {
  .header-left {
    gap: 12px;
  }

  .breadcrumb {
    display: none;
  }

  .username {
    display: none;
  }

  .main-content {
    padding: 16px;
  }
}

/* Element Plus 样式覆盖 */
:deep(.el-menu) {
  border-right: none;
}

:deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
  border-radius: 0 25px 25px 0;
  margin: 2px 8px 2px 0;
  transition: all 0.3s;
}

:deep(.el-menu-item:hover) {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

:deep(.el-menu-item.is-active) {
  background-color: #409EFF !important;
  color: #ffffff !important;
}

:deep(.el-sub-menu__title) {
  height: 50px;
  line-height: 50px;
  border-radius: 0 25px 25px 0;
  margin: 2px 8px 2px 0;
  transition: all 0.3s;
}

:deep(.el-sub-menu__title:hover) {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-dropdown-menu__item.is-active) {
  background-color: #f0f9ff;
  color: #409EFF;
}
</style>

<style scoped>
.admin-layout {
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  transition: width 0.3s;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3a4b;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.collapse-btn {
  font-size: 18px;
  color: #606266;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  color: #606266;
  font-size: 14px;
}

.main-content {
  background-color: #f0f2f5;
  padding: 20px;
}

:deep(.el-menu) {
  border-right: none;
}

:deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
}

:deep(.el-menu-item.is-active) {
  background-color: #409EFF !important;
}
</style>
