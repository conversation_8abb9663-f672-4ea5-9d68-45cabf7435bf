<template>
  <el-drawer
    v-model="visible"
    title="主题设置"
    direction="rtl"
    size="300px"
    :before-close="handleClose"
  >
    <div class="theme-drawer">
      <!-- 预设主题 -->
      <div class="setting-section">
        <h4 class="section-title">预设主题</h4>
        <div class="theme-presets">
          <div
            v-for="(preset, key) in presetThemes"
            :key="key"
            class="theme-preset"
            :class="{ active: currentTheme === key }"
            @click="handleThemeChange(key)"
          >
            <div class="preset-preview">
              <div class="preview-header" :style="{ backgroundColor: preset.headerBg }"></div>
              <div class="preview-sidebar" :style="{ backgroundColor: preset.sidebarBg }"></div>
              <div class="preview-content"></div>
            </div>
            <span class="preset-name">{{ getThemeName(key) }}</span>
          </div>
        </div>
      </div>

      <!-- 主色-->
      <div class="setting-section">
        <h4 class="section-title">主色</h4>
        <div class="color-picker-wrapper">
          <el-color-picker
            v-model="localConfig.primaryColor"
            @change="handleColorChange('primaryColor', $event)"
            show-alpha
          />
          <span class="color-value">{{ localConfig.primaryColor }}</span>
        </div>
      </div>

      <!-- 头部设置 -->
      <div class="setting-section">
        <h4 class="section-title">头部设置</h4>
        <div class="setting-item">
          <span class="setting-label">背景</span>
          <el-color-picker
            v-model="localConfig.headerBg"
            @change="handleColorChange('headerBg', $event)"
          />
        </div>
        <div class="setting-item">
          <span class="setting-label">文字</span>
          <el-color-picker
            v-model="localConfig.headerTextColor"
            @change="handleColorChange('headerTextColor', $event)"
          />
        </div>
      </div>

      <!-- 侧边栏设置-->
      <div class="setting-section">
        <h4 class="section-title">侧边栏设置</h4>
        <div class="setting-item">
          <span class="setting-label">背景</span>
          <el-color-picker
            v-model="localConfig.sidebarBg"
            @change="handleColorChange('sidebarBg', $event)"
          />
        </div>
        <div class="setting-item">
          <span class="setting-label">文字</span>
          <el-color-picker
            v-model="localConfig.sidebarTextColor"
            @change="handleColorChange('sidebarTextColor', $event)"
          />
        </div>
      </div>

      <!-- 界面设置 -->
      <div class="setting-section">
        <h4 class="section-title">界面设置</h4>
        <div class="setting-item">
          <span class="setting-label">显示底部</span>
          <el-switch
            v-model="localConfig.showFooter"
            @change="handleSwitchChange('showFooter', $event)"
          />
        </div>
        <div class="setting-item">
          <span class="setting-label">显示面包屑</span>
          <el-switch
            v-model="localConfig.showBreadcrumb"
            @change="handleSwitchChange('showBreadcrumb', $event)"
          />
        </div>
        <div class="setting-item">
          <span class="setting-label">显示标签</span>
          <el-switch
            v-model="localConfig.showTabs"
            @change="handleSwitchChange('showTabs', $event)"
          />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="setting-actions">
        <el-button @click="handleReset" type="info" plain>重置</el-button>
        <el-button @click="handleCopy" type="primary" plain>复制配置</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup >
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useThemeStore } from '../../stores/theme'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  }
})

const emit = defineEmits(['update:modelValue'])

const themeStore = useThemeStore()

// 本地配置副本
const localConfig = ref({ ...themeStore.config })

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const currentTheme = computed(() => themeStore.theme)
const presetThemes = computed(() => themeStore.presetThemes)

// 监听主题store变化
watch(() => themeStore.config, (newConfig) => {
  localConfig.value = { ...newConfig }
}, { deep: true })

// 方法
const handleClose = () => {
  visible.value = false
}

const getThemeName = (key) => {
  const names = {
    light: '明亮主题',
    dark: '暗黑主题',
    blue: '蓝色主题'
  }
  return names[key] || key
}

const handleThemeChange = (themeName) => {
  themeStore.setTheme(themeName)
}

const handleColorChange = (key, value) => {
  if (value) {
    themeStore.updateConfig({ [key]: value })
  }
}

const handleSwitchChange = (key, value) => {
  themeStore.updateConfig({ [key]: value })
}

const handleReset = () => {
  themeStore.resetTheme()
  ElMessage.success('主题已重置')
}

const handleCopy = async () => {
  try {
    const configText = JSON.stringify(themeStore.config, null, 2)
    await navigator.clipboard.writeText(configText)
    ElMessage.success('配置已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}
</script>

<style scoped>
.theme-drawer {
  padding: 0 16px;
}

.setting-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.theme-presets {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.theme-preset {
  cursor: pointer;
  border: 2px solid transparent;
  border-radius: 8px;
  padding: 8px;
  transition: all 0.3s;
  text-align: center;
}

.theme-preset:hover {
  border-color: #409EFF;
}

.theme-preset.active {
  border-color: #409EFF;
  background-color: #f0f9ff;
}

.preset-preview {
  width: 100%;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
}

.preview-header {
  height: 12px;
  width: 100%;
}

.preview-sidebar {
  position: absolute;
  left: 0;
  top: 12px;
  width: 20px;
  height: 48px;
}

.preview-content {
  position: absolute;
  left: 20px;
  top: 12px;
  right: 0;
  height: 48px;
  background-color: #f5f7fa;
}

.preset-name {
  font-size: 12px;
  color: #606266;
}

.color-picker-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-value {
  font-size: 12px;
  color: #909399;
  font-family: monospace;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.setting-label {
  font-size: 14px;
  color: #606266;
}

.setting-actions {
  display: flex;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.setting-actions .el-button {
  flex: 1;
}
</style>

<style scoped>
.theme-drawer {
  padding: 0 16px;
}

.setting-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.theme-presets {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.theme-preset {
  cursor: pointer;
  border: 2px solid transparent;
  border-radius: 8px;
  padding: 8px;
  transition: all 0.3s;
  text-align: center;
}

.theme-preset:hover {
  border-color: #409EFF;
}

.theme-preset.active {
  border-color: #409EFF;
  background-color: #f0f9ff;
}

.preset-preview {
  width: 100%;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
}

.preview-header {
  height: 12px;
  width: 100%;
}

.preview-sidebar {
  position: absolute;
  left: 0;
  top: 12px;
  width: 20px;
  height: 48px;
}

.preview-content {
  position: absolute;
  left: 20px;
  top: 12px;
  right: 0;
  height: 48px;
  background-color: #f5f7fa;
}

.preset-name {
  font-size: 12px;
  color: #606266;
}

.color-picker-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-value {
  font-size: 12px;
  color: #909399;
  font-family: monospace;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.setting-label {
  font-size: 14px;
  color: #606266;
}

.setting-actions {
  display: flex;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.setting-actions .el-button {
  flex: 1;
}
</style>
